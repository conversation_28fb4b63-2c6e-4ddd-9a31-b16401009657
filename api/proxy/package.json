{"name": "@guryou/api-proxy", "version": "1.0.0", "main": "index.js", "scripts": {"prebuild": "yarn neon", "build": "yarn clean && tsc -p .", "clean": "rm -rf .build", "predeploy:dev": "yarn build", "deploy:dev": "serverless deploy --stage=dev", "deploy:prod": "serverless deploy --stage=prod", "lint": "eslint --ext .js,.jsx,.ts,.tsx \"./src/**/*.{js,jsx,ts,tsx}\" --fix", "migration:add": "db-migrate create --sql-file $0", "migration:run": "db-migrate up -v", "prestart": "yarn neon", "start-sls": "SLS_DEBUG=* yarn sls offline start -s dev", "start": "tsx watch ./src/lambdas/localgql.ts", "ssh:tunnel": "ssh tunnel -Nv", "gen:neon": "yarn neon"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.474.0", "@aws-sdk/client-elastic-transcoder": "^3.476.0", "@aws-sdk/client-ses": "^3.474.0", "@aws-sdk/client-sns": "^3.476.0", "@aws-sdk/client-sqs": "^3.476.0", "@aws-sdk/s3-presigned-post": "^3.6.1", "@guryou/core": "1.0.0", "@guryou/utils": "1.0.0", "@the-neon/core": "^0.0.6", "@the-neon/gql": "^0.2.1", "@the-neon/mysql": "^0.0.12", "@the-neon/pg": "^0.2.0", "@the-neon/validation": "^0.0.8", "apollo-datasource": "^0.7.2", "apollo-server-lambda": "^2.18.1", "axios": "^0.21.1", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "btoa": "^1.2.1", "coda-js": "^4.0.1", "compare-versions": "^3.6.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "express": "^4.17.1", "graphql-middleware": "^4.0.2", "graphql-tools": "^7.0.4", "jsonwebtoken": "^8.5.1", "jwk-to-pem": "^2.0.4", "libphonenumber-js": "^1.10.41", "serverless-http": "^3.2.0", "stripe": "^8.64.0", "uuid": "^8.2.0"}, "devDependencies": {"@types/express-serve-static-core": "^4.17.19", "@types/jest": "^26.0.14", "core-js": "^3.6.4", "db-migrate": "^0.11.12", "db-migrate-mysql": "^2.1.2", "jest": "^26.5.0", "jest-html-reporters": "^2.1.2", "nodemon": "^2.0.2", "prettier": "^2.0.4", "regenerator-runtime": "^0.13.5", "serverless": "^3.33.0", "serverless-offline": "^6.8.0", "serverless-plugin-include-dependencies": "^4.1.0", "serverless-plugin-typescript": "^1.1.9", "serverless-prune-plugin": "^1.4.4", "ts-jest": "^26.4.3", "tsx": "^4.6.2"}}